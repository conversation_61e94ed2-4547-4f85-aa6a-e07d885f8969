package com.phodal.semantic.template;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * 示例提示词函数
 * 用于演示 @PromptFunction 注解的使用
 */
@Component
public class SamplePromptFunctions {
    
    private static final Logger logger = LoggerFactory.getLogger(SamplePromptFunctions.class);
    
    /**
     * 获取当前时间
     */
    @PromptFunction(
        name = "getCurrentTime",
        description = "获取当前时间",
        pluginName = "time"
    )
    public String getCurrentTime() {
        String time = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        logger.debug("getCurrentTime called, result: {}", time);
        return time;
    }
    
    /**
     * 格式化文本
     */
    @PromptFunction(
        name = "formatText",
        description = "格式化文本为大写",
        pluginName = "text"
    )
    public String formatText(
        @PromptFunctionParameter(name = "input", description = "要格式化的文本") String input
    ) {
        if (input == null) {
            return "";
        }
        String result = input.toUpperCase();
        logger.debug("formatText called with input: {}, result: {}", input, result);
        return result;
    }
    
    /**
     * 计算字符串长度
     */
    @PromptFunction(
        name = "getLength",
        description = "计算字符串长度",
        pluginName = "text"
    )
    public int getLength(
        @PromptFunctionParameter(name = "input", description = "要计算长度的文本") String input
    ) {
        int length = input != null ? input.length() : 0;
        logger.debug("getLength called with input: {}, result: {}", input, length);
        return length;
    }
    
    /**
     * 生成问候语
     */
    @PromptFunction(
        name = "greet",
        description = "生成问候语",
        pluginName = "greeting"
    )
    public String greet(
        @PromptFunctionParameter(name = "name", description = "姓名") String name,
        @PromptFunctionParameter(name = "language", description = "语言", defaultValue = "zh") String language
    ) {
        String greeting;
        switch (language.toLowerCase()) {
            case "en":
                greeting = "Hello, " + (name != null ? name : "World") + "!";
                break;
            case "zh":
            default:
                greeting = "你好，" + (name != null ? name : "世界") + "！";
                break;
        }
        
        logger.debug("greet called with name: {}, language: {}, result: {}", name, language, greeting);
        return greeting;
    }
    
    /**
     * 简单的数学计算
     */
    @PromptFunction(
        name = "add",
        description = "两个数字相加",
        pluginName = "math"
    )
    public double add(
        @PromptFunctionParameter(name = "a", description = "第一个数字") double a,
        @PromptFunctionParameter(name = "b", description = "第二个数字") double b
    ) {
        double result = a + b;
        logger.debug("add called with a: {}, b: {}, result: {}", a, b, result);
        return result;
    }
    
    /**
     * 处理复杂参数（Map）
     */
    @PromptFunction(
        name = "processData",
        description = "处理复杂数据",
        pluginName = "data"
    )
    public String processData(
        @PromptFunctionParameter(name = "data", description = "要处理的数据") Map<String, Object> data
    ) {
        if (data == null || data.isEmpty()) {
            return "无数据";
        }
        
        StringBuilder result = new StringBuilder("处理结果：");
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            result.append(entry.getKey()).append("=").append(entry.getValue()).append("; ");
        }
        
        String resultStr = result.toString();
        logger.debug("processData called with data: {}, result: {}", data, resultStr);
        return resultStr;
    }
    
    /**
     * 模拟订单查询函数（用于测试 skprompt.txt）
     */
    @PromptFunction(
        name = "inquiry_order_price",
        description = "根据订单信息查询订单价格",
        pluginName = "order_plugin"
    )
    public String inquiryOrderPrice(
        @PromptFunctionParameter(name = "input", description = "客户的询价文本") String input
    ) {
        // 模拟价格查询逻辑
        String result = "根据输入 \"" + input + "\" 查询到的价格信息：价格区间 9.0-9.5，建议价格 9.2";
        logger.debug("inquiry_order_price called with input: {}, result: {}", input, result);
        return result;
    }
    
    /**
     * 模拟订单下单函数
     */
    @PromptFunction(
        name = "place_order",
        description = "根据订单信息执行开仓、下单、行权操作",
        pluginName = "order_plugin"
    )
    public String placeOrder(
        @PromptFunctionParameter(name = "input", description = "客户的下单文本") String input
    ) {
        // 模拟下单逻辑
        String result = "订单已提交：" + input + "，订单号：ORD" + System.currentTimeMillis();
        logger.debug("place_order called with input: {}, result: {}", input, result);
        return result;
    }
    
    /**
     * 模拟订单平仓函数
     */
    @PromptFunction(
        name = "close_order",
        description = "根据合约号执行订单的平仓、撤单、卖出操作",
        pluginName = "order_plugin"
    )
    public String closeOrder(
        @PromptFunctionParameter(name = "input", description = "客户的平仓文本") String input
    ) {
        // 模拟平仓逻辑
        String result = "平仓操作已执行：" + input + "，操作时间：" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        logger.debug("close_order called with input: {}, result: {}", input, result);
        return result;
    }
}
