package com.phodal.semantic.template;

import com.phodal.semantic.tools.ToolLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 提示词模板管理器
 * 负责加载、缓存和渲染提示词模板
 * 支持 Handlebars 模板引擎和函数调用
 */
@Component
public class PromptTemplateManager {

    private static final Logger logger = LoggerFactory.getLogger(PromptTemplateManager.class);

    private final Map<String, PromptTemplate> templateCache = new ConcurrentHashMap<>();
    private final ToolLoader toolLoader;
    private final PromptFunctionRegistry functionRegistry;
    
    public PromptTemplateManager(ToolLoader toolLoader, PromptFunctionRegistry functionRegistry) {
        this.toolLoader = toolLoader;
        this.functionRegistry = functionRegistry;
    }
    
    /**
     * 获取或加载模板
     *
     * @param templatePath 模板路径
     * @return 模板实例
     */
    public PromptTemplate getTemplate(String templatePath) {
        return templateCache.computeIfAbsent(templatePath, path -> {
            try {
                logger.info("Loading template from path: {}", path);
                // 使用新的 Handlebars 模板实现
                return HandlebarsPromptTemplate.fromResource(path, functionRegistry);
            } catch (Exception e) {
                logger.error("Failed to load template from path: {}", path, e);
                throw new RuntimeException("Failed to load template: " + path, e);
            }
        });
    }
    
    /**
     * 渲染规划模板
     * 
     * @param businessType 业务类型
     * @param userInput 用户输入
     * @return 渲染后的提示词
     */
    public String renderPlanTemplate(String businessType, String userInput) {
        PromptTemplate template = getTemplate("plan/skprompt.txt");
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("input", userInput);
        
        // 获取可用工具列表
        String toolsList = generateToolsList(businessType);
        variables.put("ActionPlanner_Excluded.ListOfFunctions", toolsList);
        
        String rendered = template.render(variables);
        
        logger.debug("Rendered plan template for business: {}, input length: {}, output length: {}", 
            businessType, userInput != null ? userInput.length() : 0, rendered.length());
        
        return rendered;
    }
    
    /**
     * 渲染自定义模板
     *
     * @param templateContent 模板内容
     * @param variables 变量映射
     * @return 渲染后的字符串
     */
    public String renderCustomTemplate(String templateContent, Map<String, Object> variables) {
        PromptTemplate template = HandlebarsPromptTemplate.create(templateContent, functionRegistry);
        return template.render(variables);
    }
    
    /**
     * 生成工具列表字符串
     */
    private String generateToolsList(String businessType) {
        try {
            List<ToolCallback> tools = toolLoader.getAllAvailableTools(businessType);
            StringBuilder toolsList = new StringBuilder();

            logger.info("Generating tools list for business: {}, found {} tools", businessType, tools.size());

            for (ToolCallback tool : tools) {
                String toolName = tool.getToolDefinition().name();
                String description = tool.getToolDefinition().description();

                logger.info("Tool: {} - {}", toolName, description);

                // 格式化工具信息
                toolsList.append("// ").append(description).append("\n");
                toolsList.append(toolName).append("\n");
                toolsList.append("请求参数 \"input\": 用户输入文本.\n");
                toolsList.append("\n");
            }

            String result = toolsList.toString().trim();
            logger.info("Generated tools list:\n{}", result);
            return result;
        } catch (Exception e) {
            logger.error("Failed to generate tools list for business: {}", businessType, e);
            return "// 无可用工具";
        }
    }
    
    /**
     * 清除模板缓存
     */
    public void clearCache() {
        templateCache.clear();
        logger.info("Template cache cleared");
    }
    
    /**
     * 重新加载指定模板
     * 
     * @param templatePath 模板路径
     */
    public void reloadTemplate(String templatePath) {
        templateCache.remove(templatePath);
        logger.info("Template reloaded: {}", templatePath);
    }
    
    /**
     * 获取模板中的变量名
     * 
     * @param templatePath 模板路径
     * @return 变量名集合
     */
    public java.util.Set<String> getTemplateVariables(String templatePath) {
        PromptTemplate template = getTemplate(templatePath);
        return template.getVariableNames();
    }
    
    /**
     * 验证模板是否有效
     *
     * @param templatePath 模板路径
     * @return 是否有效
     */
    public boolean validateTemplate(String templatePath) {
        try {
            PromptTemplate template = getTemplate(templatePath);
            // Handlebars 模板在创建时就会验证语法
            return template != null;
        } catch (Exception e) {
            logger.error("Template validation failed for path: {}", templatePath, e);
            return false;
        }
    }
    
    /**
     * 获取缓存统计信息
     * 
     * @return 缓存信息
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("cacheSize", templateCache.size());
        stats.put("cachedTemplates", templateCache.keySet());
        return stats;
    }
}
