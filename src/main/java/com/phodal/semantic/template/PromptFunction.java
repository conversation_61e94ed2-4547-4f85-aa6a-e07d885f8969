package com.phodal.semantic.template;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 标记可以在模板中调用的函数的注解
 * 类似于 SemanticKernel 的 KernelFunction，用于在 Handlebars 模板中调用 Java 方法
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface PromptFunction {
    
    /**
     * 函数名称，如果不指定则使用方法名
     * 在模板中通过 {{functionName}} 或 {{pluginName.functionName}} 调用
     */
    String name() default "";
    
    /**
     * 函数描述，用于文档和调试
     */
    String description() default "";
    
    /**
     * 插件名称，用于命名空间
     * 如果指定，则在模板中通过 {{pluginName.functionName}} 调用
     */
    String pluginName() default "";
    
    /**
     * 返回值类型描述
     */
    String returnDescription() default "";
    
    /**
     * 是否为异步函数
     */
    boolean async() default false;
}
