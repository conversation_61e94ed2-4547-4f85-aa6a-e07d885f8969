package com.phodal.semantic.template;

import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 简单的 Handlebars 测试
 */
class SimpleHandlebarsTest {
    
    @Test
    void testBasicVariableReplacement() {
        PromptFunctionRegistry registry = new PromptFunctionRegistry();
        
        String template = "Hello {{$name}}, welcome to {{$place}}!";
        HandlebarsPromptTemplate promptTemplate = HandlebarsPromptTemplate.create(template, registry);
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("name", "Alice");
        variables.put("place", "Wonderland");
        
        String result = promptTemplate.render(variables);
        System.out.println("Result: " + result);
        assertEquals("Hello Alice, welcome to Wonderland!", result);
    }
    
    @Test
    void testSpecialVariables() {
        PromptFunctionRegistry registry = new PromptFunctionRegistry();

        String template = "Functions: {{ActionPlanner_Excluded.ListOfFunctions}}";
        System.out.println("Original template: " + template);

        HandlebarsPromptTemplate promptTemplate = HandlebarsPromptTemplate.create(template, registry);
        System.out.println("Template object created");

        Map<String, Object> variables = new HashMap<>();
        variables.put("ActionPlanner_Excluded.ListOfFunctions", "function1\nfunction2");
        System.out.println("Variables: " + variables);

        try {
            String result = promptTemplate.render(variables);
            System.out.println("Special variables result: " + result);
            assertEquals("Functions: function1\nfunction2", result);
        } catch (Exception e) {
            System.err.println("Error during rendering: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
    
    @Test
    void testFunctionRegistration() {
        PromptFunctionRegistry registry = new PromptFunctionRegistry();
        SamplePromptFunctions sampleFunctions = new SamplePromptFunctions();
        
        // 手动注册一个函数
        try {
            registry.registerFunction(sampleFunctions, "getCurrentTime", "getCurrentTime", "time");
            System.out.println("Registered function: time.getCurrentTime");
            System.out.println("Total functions: " + registry.getAllFunctions().size());
        } catch (Exception e) {
            fail("Failed to register function: " + e.getMessage());
        }
        
        // 测试函数调用
        Object result = registry.invokeFunction("time.getCurrentTime", new HashMap<>());
        assertNotNull(result);
        System.out.println("Function result: " + result);
    }
    
    @Test
    void testSimpleFunctionCall() {
        PromptFunctionRegistry registry = new PromptFunctionRegistry();
        SamplePromptFunctions sampleFunctions = new SamplePromptFunctions();
        
        // 注册函数
        try {
            registry.registerFunction(sampleFunctions, "getCurrentTime", "getCurrentTime", "time");
        } catch (Exception e) {
            fail("Failed to register function: " + e.getMessage());
        }
        
        // 测试简单的函数调用模板
        String template = "Current time: {{time.getCurrentTime}}";
        System.out.println("Function call template: " + template);
        System.out.println("Registry has functions: " + registry.getAllFunctions().keySet());

        HandlebarsPromptTemplate promptTemplate = HandlebarsPromptTemplate.create(template, registry);

        Map<String, Object> variables = new HashMap<>();

        try {
            String result = promptTemplate.render(variables);
            System.out.println("Function call result: " + result);
            assertTrue(result.startsWith("Current time: "));
            assertTrue(result.contains("-")); // 日期格式包含连字符
        } catch (Exception e) {
            System.err.println("Error during function call: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
    
    @Test
    void testDebugTemplate() {
        PromptFunctionRegistry registry = new PromptFunctionRegistry();
        
        String template = "Hello {{name}}";
        HandlebarsPromptTemplate promptTemplate = HandlebarsPromptTemplate.create(template, registry);
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("name", "World");
        
        String result = promptTemplate.render(variables);
        System.out.println("Debug result: " + result);
        assertEquals("Hello World", result);
    }
}
