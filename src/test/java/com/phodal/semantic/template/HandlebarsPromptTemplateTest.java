package com.phodal.semantic.template;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * HandlebarsPromptTemplate 测试类
 */
class HandlebarsPromptTemplateTest {
    
    private PromptFunctionRegistry functionRegistry;
    private SamplePromptFunctions sampleFunctions;
    
    @BeforeEach
    void setUp() {
        // 创建函数注册器和示例函数
        functionRegistry = new PromptFunctionRegistry();
        sampleFunctions = new SamplePromptFunctions();
        
        // 手动注册示例函数（在实际应用中会自动扫描）
        registerSampleFunctions();
    }
    
    private void registerSampleFunctions() {
        // 手动注册示例函数
        try {
            functionRegistry.registerFunction(sampleFunctions, "getCurrentTime", "getCurrentTime", "time");
            functionRegistry.registerFunction(sampleFunctions, "formatText", "formatText", "text");
        } catch (Exception e) {
            throw new RuntimeException("Failed to register sample functions", e);
        }
    }
    
    @Test
    void testBasicVariableReplacement() {
        String template = "Hello {{$name}}, welcome to {{$place}}!";
        HandlebarsPromptTemplate promptTemplate = HandlebarsPromptTemplate.create(template, functionRegistry);
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("name", "Alice");
        variables.put("place", "Wonderland");
        
        String result = promptTemplate.render(variables);
        assertEquals("Hello Alice, welcome to Wonderland!", result);
    }
    
    @Test
    void testVariableExtraction() {
        String template = "Hello {{$name}}, today is {{$date}}, and {{$weather}} weather.";
        HandlebarsPromptTemplate promptTemplate = HandlebarsPromptTemplate.create(template, functionRegistry);
        
        Set<String> variables = promptTemplate.getVariableNames();
        assertTrue(variables.contains("name"));
        assertTrue(variables.contains("date"));
        assertTrue(variables.contains("weather"));
        assertEquals(3, variables.size());
    }
    
    @Test
    void testFunctionCall() {
        String template = "Current time: {{time.getCurrentTime}}";
        HandlebarsPromptTemplate promptTemplate = HandlebarsPromptTemplate.create(template, functionRegistry);

        Map<String, Object> variables = new HashMap<>();
        String result = promptTemplate.render(variables);

        assertTrue(result.startsWith("Current time: "));
        assertTrue(result.contains("-")); // 日期格式包含连字符
    }
    
    @Test
    void testMixedVariablesAndFunctions() {
        String template = "Hello {{$name}}, current time is {{time.getCurrentTime}}.";
        HandlebarsPromptTemplate promptTemplate = HandlebarsPromptTemplate.create(template, functionRegistry);

        Map<String, Object> variables = new HashMap<>();
        variables.put("name", "Bob");

        String result = promptTemplate.render(variables);
        assertTrue(result.startsWith("Hello Bob, current time is "));
    }
    
    @Test
    void testEmptyTemplate() {
        HandlebarsPromptTemplate promptTemplate = HandlebarsPromptTemplate.create("", functionRegistry);
        String result = promptTemplate.render(new HashMap<>());
        assertEquals("", result);
    }
    
    @Test
    void testNullVariables() {
        String template = "Hello {{$name}}!";
        HandlebarsPromptTemplate promptTemplate = HandlebarsPromptTemplate.create(template, functionRegistry);
        
        String result = promptTemplate.render(null);
        assertEquals("Hello !", result);
    }
    
    @Test
    void testMissingVariable() {
        String template = "Hello {{$name}}, age is {{$age}}.";
        HandlebarsPromptTemplate promptTemplate = HandlebarsPromptTemplate.create(template, functionRegistry);
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("name", "Charlie");
        // age 变量缺失
        
        String result = promptTemplate.render(variables);
        assertEquals("Hello Charlie, age is .", result);
    }
    
    @Test
    void testSpecialVariables() {
        String template = "Functions: {{ActionPlanner_Excluded.ListOfFunctions}}";
        HandlebarsPromptTemplate promptTemplate = HandlebarsPromptTemplate.create(template, functionRegistry);
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("ActionPlanner_Excluded.ListOfFunctions", "function1\nfunction2");
        
        String result = promptTemplate.render(variables);
        assertEquals("Functions: function1\nfunction2", result);
    }
    
    @Test
    void testComplexTemplate() {
        String template = """
            系统: {{$systemRole}}
            用户: {{$userName}}
            时间: {{time.getCurrentTime}}

            问题: {{$input}}

            工具列表:
            {{ActionPlanner_Excluded.ListOfFunctions}}
            """;

        HandlebarsPromptTemplate promptTemplate = HandlebarsPromptTemplate.create(template, functionRegistry);

        Map<String, Object> variables = new HashMap<>();
        variables.put("systemRole", "智能助手");
        variables.put("userName", "用户123");
        variables.put("input", "今天天气怎么样？");
        variables.put("ActionPlanner_Excluded.ListOfFunctions", "weather.getWeather\ntime.getCurrentTime");

        String result = promptTemplate.render(variables);

        assertTrue(result.contains("系统: 智能助手"));
        assertTrue(result.contains("用户: 用户123"));
        assertTrue(result.contains("问题: 今天天气怎么样？"));
        assertTrue(result.contains("weather.getWeather"));
        assertTrue(result.contains("时间: 20")); // 包含年份
    }
    
    @Test
    void testGetTemplate() {
        String originalTemplate = "Hello {{$name}}!";
        HandlebarsPromptTemplate promptTemplate = HandlebarsPromptTemplate.create(originalTemplate, functionRegistry);
        
        assertEquals(originalTemplate, promptTemplate.getTemplate());
    }
}
