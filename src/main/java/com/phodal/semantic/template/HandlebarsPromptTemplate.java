package com.phodal.semantic.template;

import com.github.jknack.handlebars.Context;
import com.github.jknack.handlebars.EscapingStrategy;
import com.github.jknack.handlebars.Handlebars;
import com.github.jknack.handlebars.Helper;
import com.github.jknack.handlebars.Options;
import com.github.jknack.handlebars.ValueResolver;
import com.github.jknack.handlebars.context.JavaBeanValueResolver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 基于 Handlebars 的提示词模板实现
 * 支持函数调用和变量替换
 */
public class HandlebarsPromptTemplate implements PromptTemplate {
    
    private static final Logger logger = LoggerFactory.getLogger(HandlebarsPromptTemplate.class);
    
    // 匹配 {{$variableName}} 格式的变量
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\{\\{\\s*\\$([a-zA-Z0-9_]+)\\s*\\}\\}");
    
    // 匹配 {{functionName}} 或 {{pluginName.functionName}} 格式的函数调用
    private static final Pattern FUNCTION_PATTERN = Pattern.compile("\\{\\{\\s*([a-zA-Z0-9_]+(?:\\.[a-zA-Z0-9_]+)?)\\s*\\}\\}");
    
    private final String template;
    private final Set<String> variableNames;
    private final Handlebars handlebars;
    private final PromptFunctionRegistry functionRegistry;
    
    public HandlebarsPromptTemplate(String template, PromptFunctionRegistry functionRegistry) {
        this.template = template;
        this.functionRegistry = functionRegistry;
        this.variableNames = extractVariableNames(template);
        this.handlebars = createHandlebars();
    }
    
    /**
     * 创建和配置 Handlebars 实例
     */
    private Handlebars createHandlebars() {
        Handlebars hb = new Handlebars();

        // 设置转义策略为 XML（类似 SemanticKernel）
        hb.with(EscapingStrategy.XML);

        // 注册自定义 lookup helper 来处理带点的变量名
        hb.registerHelper("lookup", (context, options) -> {
            if (options.params.length > 0) {
                String key = options.params[0].toString();
                if (context instanceof Map) {
                    return ((Map<?, ?>) context).get(key);
                }
            }
            return null;
        });

        // 注册函数 helpers
        addFunctionHelpers(hb);

        return hb;
    }
    
    /**
     * 添加函数 helpers
     */
    private void addFunctionHelpers(Handlebars handlebars) {
        if (functionRegistry == null) {
            logger.warn("Function registry is null, function calls will not be available");
            return;
        }

        Map<String, PromptFunctionRegistry.RegisteredFunction> functions = functionRegistry.getAllFunctions();

        for (Map.Entry<String, PromptFunctionRegistry.RegisteredFunction> entry : functions.entrySet()) {
            String functionName = entry.getKey();
            PromptFunctionRegistry.RegisteredFunction function = entry.getValue();

            // 将点号替换为下划线，因为 Handlebars helper 名称不能包含点号
            String helperName = functionName.replace(".", "_");
            handlebars.registerHelper(helperName, createFunctionHelper(function));

            logger.debug("Registered Handlebars helper: {} for function: {}", helperName, functionName);
        }

        logger.info("Registered {} function helpers", functions.size());
    }
    
    /**
     * 创建函数调用 helper
     */
    private Helper<Object> createFunctionHelper(PromptFunctionRegistry.RegisteredFunction function) {
        return (context, options) -> {
            try {
                Map<String, Object> parameters = new HashMap<>();

                // 从 context 获取参数
                if (context instanceof Map) {
                    parameters.putAll((Map<String, Object>) context);
                } else if (context != null) {
                    parameters.put("input", context);
                }

                // 从 options.hash 获取命名参数
                if (options.hash != null) {
                    parameters.putAll(options.hash);
                }

                logger.debug("Calling function {} with parameters: {}", function.getFullName(), parameters);

                // 调用函数
                Object result = function.invoke(parameters);

                logger.debug("Function {} result: {}", function.getFullName(), result);

                return result != null ? result.toString() : "";

            } catch (Exception e) {
                logger.error("Failed to call function: {}", function.getFullName(), e);
                return "[ERROR: " + e.getMessage() + "]";
            }
        };
    }
    
    @Override
    public String render(Map<String, Object> variables) {
        if (template == null || template.isEmpty()) {
            return "";
        }
        
        if (variables == null) {
            variables = new HashMap<>();
        }
        
        try {
            // 预处理模板，将 {{$variable}} 转换为 {{variable}}
            String processedTemplate = preprocessTemplate(template);
            
            // 创建 context
            ArrayList<ValueResolver> resolvers = new ArrayList<>();
            resolvers.add(new VariableResolver());
            resolvers.add(JavaBeanValueResolver.INSTANCE);
            
            Context context = Context
                .newBuilder(variables)
                .resolver(resolvers.toArray(new ValueResolver[0]))
                .build();
            
            // 编译并应用模板
            String result = handlebars.compileInline(processedTemplate).apply(context);
            
            logger.debug("Template rendered successfully, input length: {}, output length: {}", 
                template.length(), result.length());
            
            return result;
            
        } catch (IOException e) {
            logger.error("Failed to render template", e);
            throw new RuntimeException("Failed to render template", e);
        }
    }
    
    /**
     * 预处理模板，将 {{$variable}} 转换为 {{variable}}
     */
    private String preprocessTemplate(String template) {
        String result = template;

        // 将 {{$variableName}} 转换为 {{variableName}}
        result = result.replaceAll("\\{\\{\\s*\\$([a-zA-Z0-9_]+)\\s*\\}\\}", "{{$1}}");

        // 首先处理函数调用（简单的 plugin.function 格式），将点号替换为下划线
        // 只匹配已知的函数调用模式
        if (functionRegistry != null) {
            Map<String, PromptFunctionRegistry.RegisteredFunction> functions = functionRegistry.getAllFunctions();
            for (String functionName : functions.keySet()) {
                if (functionName.contains(".")) {
                    String helperName = functionName.replace(".", "_");
                    result = result.replace("{{" + functionName + "}}", "{{" + helperName + "}}");
                    result = result.replace("{{ " + functionName + " }}", "{{ " + helperName + " }}");
                }
            }
        }

        // 处理特殊变量名（带点的），使用 lookup helper
        // 这个正则表达式现在只会匹配不是函数调用的变量
        result = result.replaceAll("\\{\\{\\s*([a-zA-Z0-9_]+\\.[a-zA-Z0-9_\\.]+)\\s*\\}\\}", "{{lookup this \"$1\"}}");

        return result;
    }
    
    @Override
    public Set<String> getVariableNames() {
        return new HashSet<>(variableNames);
    }
    
    @Override
    public String getTemplate() {
        return template;
    }
    
    /**
     * 从模板中提取所有变量名
     */
    private Set<String> extractVariableNames(String template) {
        Set<String> names = new HashSet<>();
        
        if (template == null || template.isEmpty()) {
            return names;
        }
        
        // 提取 {{$variableName}} 格式的变量
        Matcher matcher = VARIABLE_PATTERN.matcher(template);
        while (matcher.find()) {
            names.add(matcher.group(1));
        }
        
        // 提取其他格式的变量（不以 $ 开头的）
        Matcher functionMatcher = FUNCTION_PATTERN.matcher(template);
        while (functionMatcher.find()) {
            String name = functionMatcher.group(1);
            // 如果不是函数调用，则认为是变量
            if (functionRegistry == null || functionRegistry.getFunction(name) == null) {
                names.add(name);
            }
        }
        
        return names;
    }
    
    /**
     * 创建模板实例的工厂方法
     */
    public static HandlebarsPromptTemplate create(String template, PromptFunctionRegistry functionRegistry) {
        return new HandlebarsPromptTemplate(template, functionRegistry);
    }
    
    /**
     * 从资源文件加载模板
     */
    public static HandlebarsPromptTemplate fromResource(String resourcePath, PromptFunctionRegistry functionRegistry) {
        try {
            org.springframework.core.io.ClassPathResource resource = 
                new org.springframework.core.io.ClassPathResource(resourcePath);
            String template = resource.getContentAsString(java.nio.charset.StandardCharsets.UTF_8);
            return new HandlebarsPromptTemplate(template, functionRegistry);
        } catch (Exception e) {
            logger.error("Failed to load template from resource: {}", resourcePath, e);
            throw new RuntimeException("Failed to load template from resource: " + resourcePath, e);
        }
    }
    
    /**
     * 变量解析器
     */
    private static class VariableResolver implements ValueResolver {

        @Override
        public Object resolve(Object context, String name) {
            if (context instanceof Map) {
                Map<?, ?> map = (Map<?, ?>) context;
                // 直接查找
                Object value = map.get(name);
                if (value != null) {
                    return value;
                }

                // 处理带点的变量名（如 ActionPlanner_Excluded.ListOfFunctions）
                for (Map.Entry<?, ?> entry : map.entrySet()) {
                    if (entry.getKey() != null && entry.getKey().toString().equals(name)) {
                        return entry.getValue();
                    }
                }
            }
            return UNRESOLVED;
        }

        @Override
        public Object resolve(Object context) {
            return context;
        }

        @Override
        public Set<Map.Entry<String, Object>> propertySet(Object context) {
            if (context instanceof Map) {
                return ((Map<String, Object>) context).entrySet();
            }
            return new HashSet<>();
        }
    }
}
