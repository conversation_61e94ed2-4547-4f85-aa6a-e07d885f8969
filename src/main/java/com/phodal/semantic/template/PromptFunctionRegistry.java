package com.phodal.semantic.template;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 函数注册器，用于发现和注册带有 @PromptFunction 注解的方法
 */
@Component
public class PromptFunctionRegistry implements ApplicationContextAware {
    
    private static final Logger logger = LoggerFactory.getLogger(PromptFunctionRegistry.class);
    
    private ApplicationContext applicationContext;
    private final Map<String, RegisteredFunction> functions = new ConcurrentHashMap<>();
    
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        scanAndRegisterFunctions();
    }
    
    /**
     * 扫描并注册所有带有 @PromptFunction 注解的方法
     */
    private void scanAndRegisterFunctions() {
        logger.info("Scanning for @PromptFunction annotated methods...");
        
        String[] beanNames = applicationContext.getBeanDefinitionNames();
        for (String beanName : beanNames) {
            try {
                Object bean = applicationContext.getBean(beanName);
                Class<?> beanClass = bean.getClass();
                
                // 跳过代理类，获取真实类
                if (beanClass.getName().contains("$$")) {
                    beanClass = beanClass.getSuperclass();
                }
                
                Method[] methods = beanClass.getDeclaredMethods();
                for (Method method : methods) {
                    PromptFunction annotation = method.getAnnotation(PromptFunction.class);
                    if (annotation != null) {
                        registerFunction(bean, method, annotation);
                    }
                }
            } catch (Exception e) {
                logger.warn("Failed to scan bean: {}", beanName, e);
            }
        }
        
        logger.info("Registered {} prompt functions", functions.size());
    }
    
    /**
     * 注册单个函数
     */
    private void registerFunction(Object bean, Method method, PromptFunction annotation) {
        String functionName = annotation.name().isEmpty() ? method.getName() : annotation.name();
        String pluginName = annotation.pluginName();
        
        String fullName = pluginName.isEmpty() ? functionName : pluginName + "." + functionName;
        
        RegisteredFunction registeredFunction = new RegisteredFunction(
            bean, method, annotation, fullName, functionName, pluginName
        );
        
        functions.put(fullName, registeredFunction);
        
        logger.debug("Registered function: {} -> {}.{}", 
            fullName, bean.getClass().getSimpleName(), method.getName());
    }
    
    /**
     * 获取注册的函数
     */
    public RegisteredFunction getFunction(String name) {
        return functions.get(name);
    }
    
    /**
     * 获取所有注册的函数
     */
    public Map<String, RegisteredFunction> getAllFunctions() {
        return new HashMap<>(functions);
    }
    
    /**
     * 调用函数
     */
    public Object invokeFunction(String name, Map<String, Object> parameters) {
        RegisteredFunction function = getFunction(name);
        if (function == null) {
            throw new IllegalArgumentException("Function not found: " + name);
        }

        return function.invoke(parameters);
    }

    /**
     * 手动注册函数（用于测试）
     */
    public void registerFunction(Object bean, String methodName, String functionName, String pluginName) {
        try {
            Method[] methods = bean.getClass().getDeclaredMethods();
            for (Method method : methods) {
                if (method.getName().equals(methodName)) {
                    PromptFunction annotation = method.getAnnotation(PromptFunction.class);
                    if (annotation != null) {
                        String fullName = pluginName + "." + functionName;
                        RegisteredFunction registeredFunction = new RegisteredFunction(
                            bean, method, annotation, fullName, functionName, pluginName
                        );
                        functions.put(fullName, registeredFunction);
                        logger.debug("Manually registered function: {}", fullName);
                        return;
                    }
                }
            }
            throw new IllegalArgumentException("Method not found or not annotated: " + methodName);
        } catch (Exception e) {
            throw new RuntimeException("Failed to register function: " + methodName, e);
        }
    }
    
    /**
     * 注册的函数信息
     */
    public static class RegisteredFunction {
        private final Object bean;
        private final Method method;
        private final PromptFunction annotation;
        private final String fullName;
        private final String functionName;
        private final String pluginName;
        
        public RegisteredFunction(Object bean, Method method, PromptFunction annotation,
                                String fullName, String functionName, String pluginName) {
            this.bean = bean;
            this.method = method;
            this.annotation = annotation;
            this.fullName = fullName;
            this.functionName = functionName;
            this.pluginName = pluginName;
            
            // 确保方法可访问
            method.setAccessible(true);
        }
        
        /**
         * 调用函数
         */
        public Object invoke(Map<String, Object> parameters) {
            try {
                Parameter[] methodParams = method.getParameters();
                Object[] args = new Object[methodParams.length];
                
                for (int i = 0; i < methodParams.length; i++) {
                    Parameter param = methodParams[i];
                    PromptFunctionParameter paramAnnotation = param.getAnnotation(PromptFunctionParameter.class);
                    
                    String paramName = paramAnnotation != null && !paramAnnotation.name().isEmpty() 
                        ? paramAnnotation.name() 
                        : param.getName();
                    
                    Object value = parameters.get(paramName);
                    
                    // 处理默认值
                    if (value == null && paramAnnotation != null && !paramAnnotation.defaultValue().isEmpty()) {
                        value = paramAnnotation.defaultValue();
                    }
                    
                    // 类型转换
                    args[i] = convertParameter(value, param.getType());
                }
                
                return method.invoke(bean, args);
            } catch (Exception e) {
                throw new RuntimeException("Failed to invoke function: " + fullName, e);
            }
        }
        
        /**
         * 参数类型转换
         */
        private Object convertParameter(Object value, Class<?> targetType) {
            if (value == null) {
                return null;
            }
            
            if (targetType.isAssignableFrom(value.getClass())) {
                return value;
            }
            
            // 基本类型转换
            if (targetType == String.class) {
                return value.toString();
            } else if (targetType == int.class || targetType == Integer.class) {
                return Integer.valueOf(value.toString());
            } else if (targetType == long.class || targetType == Long.class) {
                return Long.valueOf(value.toString());
            } else if (targetType == double.class || targetType == Double.class) {
                return Double.valueOf(value.toString());
            } else if (targetType == boolean.class || targetType == Boolean.class) {
                return Boolean.valueOf(value.toString());
            }
            
            return value;
        }
        
        // Getters
        public Object getBean() { return bean; }
        public Method getMethod() { return method; }
        public PromptFunction getAnnotation() { return annotation; }
        public String getFullName() { return fullName; }
        public String getFunctionName() { return functionName; }
        public String getPluginName() { return pluginName; }
    }
}
