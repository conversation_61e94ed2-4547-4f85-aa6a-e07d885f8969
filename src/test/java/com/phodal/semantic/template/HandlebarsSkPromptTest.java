package com.phodal.semantic.template;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试 skprompt.txt 模板的函数调用功能
 */
@SpringBootTest
@SpringJUnitConfig
class HandlebarsSkPromptTest {
    
    private PromptFunctionRegistry functionRegistry;
    private SamplePromptFunctions sampleFunctions;
    private PromptTemplateManager templateManager;
    
    @BeforeEach
    void setUp() {
        // 创建函数注册器和示例函数
        functionRegistry = new PromptFunctionRegistry();
        sampleFunctions = new SamplePromptFunctions();
        
        // 手动注册示例函数
        registerSampleFunctions();
        
        // 创建模板管理器
        templateManager = new PromptTemplateManager(null, functionRegistry);
    }
    
    private void registerSampleFunctions() {
        try {
            // 注册订单相关函数
            registerFunction("inquiry_order_price", "order_plugin");
            registerFunction("place_order", "order_plugin");
            registerFunction("close_order", "order_plugin");
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to register sample functions", e);
        }
    }
    
    private void registerFunction(String methodName, String pluginName) throws Exception {
        String javaMethodName = methodName.replace("_", "");
        // 特殊处理一些方法名映射
        if (javaMethodName.equals("inquiryorderprice")) {
            javaMethodName = "inquiryOrderPrice";
        } else if (javaMethodName.equals("placeorder")) {
            javaMethodName = "placeOrder";
        } else if (javaMethodName.equals("closeorder")) {
            javaMethodName = "closeOrder";
        }

        functionRegistry.registerFunction(sampleFunctions, javaMethodName, methodName, pluginName);
    }
    
    @Test
    void testSkPromptTemplateLoading() {
        // 测试从资源文件加载模板
        assertDoesNotThrow(() -> {
            PromptTemplate template = templateManager.getTemplate("plan/skprompt.txt");
            assertNotNull(template);
            assertNotNull(template.getTemplate());
            assertTrue(template.getTemplate().length() > 0);
        });
    }
    
    @Test
    void testSkPromptVariableExtraction() {
        PromptTemplate template = templateManager.getTemplate("plan/skprompt.txt");
        
        var variableNames = template.getVariableNames();
        
        // 检查是否包含预期的变量
        assertTrue(variableNames.contains("input"));
        assertTrue(variableNames.contains("ActionPlanner_Excluded.ListOfFunctions"));
        
        System.out.println("发现的变量: " + variableNames);
    }
    
    @Test
    void testSkPromptBasicRendering() {
        PromptTemplate template = templateManager.getTemplate("plan/skprompt.txt");
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("input", "询：600105永鼎股份，虚值5%看涨，2个月，300万名义本金");
        variables.put("ActionPlanner_Excluded.ListOfFunctions", 
            "// 根据订单信息查询订单价格\n" +
            "order_plugin.inquiry_order_price\n" +
            "请求参数 \"input\": 客户的询价文本.\n\n" +
            "// 根据订单信息执行开仓、下单、行权操作\n" +
            "order_plugin.place_order\n" +
            "请求参数 \"input\": 客户的下单文本.");
        
        String result = template.render(variables);
        
        assertNotNull(result);
        assertTrue(result.contains("询：600105永鼎股份"));
        assertTrue(result.contains("order_plugin.inquiry_order_price"));
        assertTrue(result.contains("智能交易助手"));
        
        System.out.println("渲染结果:");
        System.out.println(result);
    }
    
    @Test
    void testFunctionCallInTemplate() {
        // 创建一个包含函数调用的简单模板
        String templateContent = """
            用户输入: {{$input}}

            处理结果: {{order_plugin_inquiry_order_price}}

            时间: {{time_getCurrentTime}}
            """;
        
        // 注册时间函数
        try {
            java.lang.reflect.Method getCurrentTimeMethod = SamplePromptFunctions.class.getMethod("getCurrentTime");
            PromptFunction annotation = getCurrentTimeMethod.getAnnotation(PromptFunction.class);
            if (annotation != null) {
                String fullName = "time.getCurrentTime";
                PromptFunctionRegistry.RegisteredFunction registeredFunction = 
                    new PromptFunctionRegistry.RegisteredFunction(
                        sampleFunctions, getCurrentTimeMethod, annotation, 
                        fullName, "getCurrentTime", "time"
                    );
                functionRegistry.getAllFunctions().put(fullName, registeredFunction);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        
        HandlebarsPromptTemplate template = HandlebarsPromptTemplate.create(templateContent, functionRegistry);
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("input", "600105永鼎股份询价");
        
        String result = template.render(variables);
        
        assertNotNull(result);
        assertTrue(result.contains("600105永鼎股份询价"));
        assertTrue(result.contains("处理结果:"));
        assertTrue(result.contains("时间:"));
        
        System.out.println("函数调用测试结果:");
        System.out.println(result);
    }
    
    @Test
    void testComplexFunctionCallScenario() {
        // 测试复杂的函数调用场景
        String templateContent = """
            智能交易助手分析:

            用户请求: {{$userInput}}

            价格查询结果: {{order_plugin_inquiry_order_price}}

            如果用户确认，可以执行: {{order_plugin_place_order}}

            分析时间: {{time_getCurrentTime}}
            """;
        
        // 注册时间函数
        try {
            java.lang.reflect.Method getCurrentTimeMethod = SamplePromptFunctions.class.getMethod("getCurrentTime");
            PromptFunction annotation = getCurrentTimeMethod.getAnnotation(PromptFunction.class);
            if (annotation != null) {
                String fullName = "time.getCurrentTime";
                PromptFunctionRegistry.RegisteredFunction registeredFunction = 
                    new PromptFunctionRegistry.RegisteredFunction(
                        sampleFunctions, getCurrentTimeMethod, annotation, 
                        fullName, "getCurrentTime", "time"
                    );
                functionRegistry.getAllFunctions().put(fullName, registeredFunction);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        
        HandlebarsPromptTemplate template = HandlebarsPromptTemplate.create(templateContent, functionRegistry);
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("userInput", "中国平安，1m/2m，200万，9070，询价");
        
        String result = template.render(variables);
        
        assertNotNull(result);
        assertTrue(result.contains("中国平安"));
        assertTrue(result.contains("价格查询结果:"));
        assertTrue(result.contains("可以执行:"));
        assertTrue(result.contains("分析时间:"));
        
        System.out.println("复杂函数调用场景测试结果:");
        System.out.println(result);
    }
    
    @Test
    void testErrorHandling() {
        // 测试函数调用错误处理
        String templateContent = "Result: {{nonexistent_function}}";
        
        HandlebarsPromptTemplate template = HandlebarsPromptTemplate.create(templateContent, functionRegistry);
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("input", "test");
        
        String result = template.render(variables);
        
        // 应该包含错误信息而不是抛出异常
        assertNotNull(result);
        assertTrue(result.contains("Result:"));
        
        System.out.println("错误处理测试结果:");
        System.out.println(result);
    }
}
