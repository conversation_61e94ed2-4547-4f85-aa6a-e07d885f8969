package com.phodal.semantic.template;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 标记函数参数的注解
 * 用于描述 @PromptFunction 方法的参数信息
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.PARAMETER)
public @interface PromptFunctionParameter {
    
    /**
     * 参数名称，如果不指定则使用参数名
     */
    String name() default "";
    
    /**
     * 参数描述
     */
    String description() default "";
    
    /**
     * 是否为必需参数
     */
    boolean required() default true;
    
    /**
     * 默认值
     */
    String defaultValue() default "";
}
